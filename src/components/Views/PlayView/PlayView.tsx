import { useState, useEffect, useRef } from "react";
import { Image, Mic, Modal } from "microapps";
import { io, Socket } from "socket.io-client";
import { useEnygmaGame } from "../../../contexts/EnygmaGameContext";
import { useQuestionsColor } from "../../../utils/questionsColorSystem";
import "./PlayView.scss";

interface PlayViewProps {
  handleShowClues: () => void;
  handleExistGame: () => void;
  showExitPopup: boolean;
  handleConfirmExit: () => void;
  handleCancelExit: () => void;
}

interface SessionMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

interface ChatMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

// Configuración crítica de audio
const audioConfig = {
  sampleRate: 16000, // Calidad óptima para voz
  channelCount: 1, // Mono para eficiencia
  sampleSize: 16, // 16-bit para calidad
  chunkSize: 1600, // 100ms chunks (crítico para fluidez)
  bufferSize: 4096,
} as const;

// Interfaces para el socket
interface TranscriptionData {
  text: string;
  confidence?: number;
  isFinal?: boolean;
}

interface ReplyData {
  text: string;
  audioUrl?: string;
}

// AudioProcessor inline (para evitar archivo externo)
const createAudioProcessor = () => {
  const audioProcessorCode = `
    class AudioProcessor extends AudioWorkletProcessor {
      constructor() {
        super();
      }

      process(inputs, outputs, parameters) {
        const input = inputs[0];
        if (input && input[0]) {
          this.port.postMessage(input[0]);
        }
        return true;
      }
    }

    registerProcessor('audio-processor', AudioProcessor);
  `;

  const blob = new Blob([audioProcessorCode], {
    type: "application/javascript",
  });
  return URL.createObjectURL(blob);
};

const PlayView: React.FC<PlayViewProps> = ({
  handleShowClues,
  handleExistGame,
  showExitPopup,
  handleConfirmExit,
  handleCancelExit,
}) => {
  const { session, askQuestion, askInitialMessage } = useEnygmaGame();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isLivesPopUpShown, setIsLivesPopUpShown] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Hook para obtener el color basado en preguntas restantes
  const questionsColorData = useQuestionsColor(
    session?.maxQuestions || 20,
    session?.questionCount || 0
  );

  // Estados para el sistema de voz
  const [isAILoading, setIsAILoading] = useState(false);
  const [micLevel, setMicLevel] = useState(0);
  const [isVoiceActive, setIsVoiceActive] = useState(false);
  const [voiceError, setVoiceError] = useState<string | null>(null);
  const [isSocketConnected, setIsSocketConnected] = useState(false);
  const [lastChunkSent, setLastChunkSent] = useState(0);

  // Referencias para audio y socket
  const socketRef = useRef<Socket | null>(null);
  const iterationRef = useRef(1);
  const audioContextRef = useRef<AudioContext | null>(null);
  const playbackSourceRef = useRef<AudioBufferSourceNode | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);
  const processorRef = useRef<AudioWorkletNode | null>(null);
  const silenceDetectionRef = useRef({
    lastSpeechTime: 0,
    isSpeaking: false,
    silenceThreshold: 0.01,
    silenceDuration: 1500, // 1.5 segundos de silencio para enviar
    minChunkInterval: 100, // Mínimo 100ms entre chunks
    accumulated: [] as number[],
  });

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Funciones de audio
  const stopAudio = () => {
    if (playbackSourceRef.current) {
      try {
        playbackSourceRef.current.stop();
        playbackSourceRef.current = null;
      } catch (error) {
        console.error("Error stopping audio:", error);
      }
    }
  };

  const playAudio = async (audioUrl: string) => {
    if (!audioContextRef.current) return;

    try {
      const response = await fetch(audioUrl);
      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer =
        await audioContextRef.current.decodeAudioData(arrayBuffer);

      if (playbackSourceRef.current) {
        stopAudio();
      }

      const source = audioContextRef.current.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(audioContextRef.current.destination);
      playbackSourceRef.current = source;
      source.start();

      source.onended = () => {
        playbackSourceRef.current = null;
      };
    } catch (error) {
      console.error("Error playing audio:", error);
    }
  };

  // Configuración del monitoreo de nivel de micrófono
  const setupMicrophoneLevelMonitoring = (
    source: MediaStreamAudioSourceNode,
    setLevel: (level: number) => void,
    onSpeechDetected?: () => void
  ) => {
    if (!audioContextRef.current) return;

    const analyser = audioContextRef.current.createAnalyser();
    analyser.fftSize = 256;
    source.connect(analyser);

    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const updateLevel = () => {
      if (!audioContextRef.current) return;

      analyser.getByteFrequencyData(dataArray);
      const average =
        dataArray.reduce((sum, value) => sum + value, 0) / bufferLength;
      const normalizedLevel = average / 255;

      setLevel(normalizedLevel);

      // Detectar habla (umbral ajustable)
      if (normalizedLevel > 0.02 && onSpeechDetected) {
        onSpeechDetected();
      }

      requestAnimationFrame(updateLevel);
    };

    updateLevel();
  };

  const setupTranscription = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: audioConfig.channelCount,
          sampleRate: audioConfig.sampleRate,
          sampleSize: audioConfig.sampleSize,
        },
      });

      audioContextRef.current = new AudioContext();
      mediaStreamRef.current = stream;

      const audioProcessorUrl = createAudioProcessor();
      await audioContextRef.current.audioWorklet.addModule(audioProcessorUrl);

      const source = audioContextRef.current.createMediaStreamSource(stream);
      const processor = new AudioWorkletNode(
        audioContextRef.current,
        "audio-processor"
      );
      processorRef.current = processor;

      const audioBuffer: number[] = [];
      let lastProcessTime = 0;

      processor.port.onmessage = (event) => {
        const audioSamples = event.data as Float32Array;
        const int16Buffer = new Int16Array(audioSamples.length);

        // Convertir samples
        for (let i = 0; i < audioSamples.length; i++) {
          int16Buffer[i] = Math.max(
            -32768,
            Math.min(32767, audioSamples[i] * 32768)
          );
        }

        audioBuffer.push(...Array.from(int16Buffer));

        // ========== CONTROL DE RATE LIMITING ==========
        const now = Date.now();
        const timeSinceLastChunk = now - lastProcessTime;

        // Solo procesar si ha pasado suficiente tiempo (100ms mínimo)
        if (
          timeSinceLastChunk >= 100 &&
          audioBuffer.length >= audioConfig.chunkSize
        ) {
          // Calcular nivel de audio para detección de voz
          const chunk = audioBuffer.slice(0, audioConfig.chunkSize);
          const avgAmplitude =
            chunk.reduce((sum, sample) => sum + Math.abs(sample), 0) /
            chunk.length;
          const normalizedLevel = avgAmplitude / 32768;

          // Solo enviar si hay suficiente audio (detección de voz)
          if (normalizedLevel > 0.01) {
            // Umbral de voz detectada
            const chunkToSend = new Int16Array(
              audioBuffer.splice(0, audioConfig.chunkSize)
            );

            if (socketRef.current?.connected) {
              console.log(
                `📤 Enviando chunk (nivel: ${normalizedLevel.toFixed(3)}, tamaño: ${chunkToSend.length} samples)`
              );
              socketRef.current.emit("audio", chunkToSend.buffer);
              lastProcessTime = now;
              setLastChunkSent(now);

              // Acumular para enviar en lotes más grandes
              silenceDetectionRef.current.accumulated.push(
                ...Array.from(chunkToSend)
              );
              silenceDetectionRef.current.lastSpeechTime = now;
              silenceDetectionRef.current.isSpeaking = true;
            }
          } else {
            // Detectar silencio
            if (
              silenceDetectionRef.current.isSpeaking &&
              now - silenceDetectionRef.current.lastSpeechTime >
                silenceDetectionRef.current.silenceDuration
            ) {
              // Enviar audio acumulado cuando se detecta fin de frase
              if (silenceDetectionRef.current.accumulated.length > 0) {
                console.log(
                  `🔇 Silencio detectado - Enviando audio completo para transcripción (${silenceDetectionRef.current.accumulated.length} samples)`
                );

                const completeAudio = new Int16Array(
                  silenceDetectionRef.current.accumulated
                );
                if (socketRef.current?.connected) {
                  console.log("📡 Emitiendo evento 'audio_complete' al servidor");
                  socketRef.current.emit(
                    "audio_complete",
                    completeAudio.buffer
                  );

                  // Timeout para verificar si el servidor responde
                  setTimeout(() => {
                    console.warn("⏰ Han pasado 5 segundos sin respuesta del servidor de transcripción");
                  }, 5000);
                } else {
                  console.error("❌ Socket no conectado al intentar enviar audio completo");
                }

                // Reset
                silenceDetectionRef.current.accumulated = [];
                silenceDetectionRef.current.isSpeaking = false;
              }
            }

            // Limpiar buffer de silencio
            audioBuffer.splice(0, audioConfig.chunkSize);
          }
        }
      };

      source.connect(processor);

      setupMicrophoneLevelMonitoring(source, setMicLevel, () => {
        if (playbackSourceRef.current) {
          stopAudio();
        }
      });

      setIsVoiceActive(true);
      setVoiceError(null);
      console.log(
        "✅ Transcription setup complete. Sample Rate:",
        audioContextRef.current.sampleRate
      );
    } catch (error) {
      console.error("❌ Error setting up transcription:", error);
      setVoiceError(
        error instanceof Error ? error.message : "Error setting up audio"
      );
      setIsVoiceActive(false);
    }
  };

  const stopTranscription = async () => {
    try {
      setIsVoiceActive(false);

      if (playbackSourceRef.current) {
        stopAudio();
      }

      if (mediaStreamRef.current) {
        const tracks = mediaStreamRef.current.getTracks();
        tracks.forEach((track) => {
          track.enabled = false;
          track.stop();
        });
        mediaStreamRef.current = null;
      }

      if (processorRef.current) {
        processorRef.current.disconnect();
        processorRef.current = null;
      }

      if (
        audioContextRef.current &&
        audioContextRef.current.state !== "closed"
      ) {
        await audioContextRef.current.close();
        audioContextRef.current = null;
      }
    } catch (error) {
      console.error("Error stopping transcription:", error);
    }
  };

  // ========== FUNCIÓN DE CONEXIÓN SOCKET ==========
  const connectSocket = async (): Promise<void> => {
    return new Promise((resolve, reject) => {
      const voiceApiUrl = import.meta.env.VITE_FLUID_VOICE_API_URL;
      const voiceApiKey = import.meta.env.VITE_FLUID_VOICE_API_KEY;

      console.log("🔧 Voice API Config:", {
        url: voiceApiUrl,
        hasKey: !!voiceApiKey,
        keyLength: voiceApiKey?.length || 0
      });

      if (!voiceApiUrl || !voiceApiKey) {
        const error = new Error("Voice API configuration not found");
        console.error("❌ Missing voice API configuration:", { voiceApiUrl, hasKey: !!voiceApiKey });
        reject(error);
        return;
      }

      try {
        console.log("🔌 Connecting to voice API:", voiceApiUrl);

        socketRef.current = io(voiceApiUrl, {
          auth: {
            apiKey: voiceApiKey,
          },
          transports: ["websocket", "polling"],
          timeout: 10000,
          forceNew: true,
          reconnection: true,
          reconnectionAttempts: 3,
          reconnectionDelay: 1000,
        });

        if (socketRef.current) {
          setupSocketListeners(socketRef.current);

          socketRef.current.on("connect", () => {
            console.log("✅ Voice API connected successfully");
            setIsSocketConnected(true);
            resolve();
          });

          socketRef.current.on("connect_error", (error) => {
            console.warn("❌ Voice API connection error:", error.message);
            setIsSocketConnected(false);
            reject(error);
          });

          socketRef.current.on("disconnect", (reason) => {
            console.log("🔌 Voice API disconnected:", reason);
            setIsSocketConnected(false);
          });
        }
      } catch (error) {
        console.error("❌ Error initializing voice socket:", error);
        reject(error);
      }
    });
  };

  // ========== SETUP DE LISTENERS DE SOCKET ==========
  const setupSocketListeners = (socket: Socket) => {
    // Log todos los eventos que llegan del socket
    socket.onAny((eventName, ...args) => {
      console.log(`🔔 Socket event received: ${eventName}`, args);
    });

    socket.on("transcription", (data: TranscriptionData) => {
      console.log("📝 Transcription received:", data);

      // Agregar mensaje del usuario al chat
      const userMessage: ChatMessage = {
        id: `user-${Date.now()}`,
        text: data.text,
        sender: "user",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, userMessage]);
    });

    socket.on("loading", () => {
      setIsAILoading(true);
      console.log("⏳ AI is processing...");
    });

    socket.on("reply", async (data: ReplyData) => {
      try {
        const { text, audioUrl } = data;
        setIsAILoading(false);

        console.log("💬 Reply received:", text);

        // Procesar diferentes tipos de respuesta
        if (text.startsWith("[") && text.includes("]")) {
          console.log("🎮 Comando especial:", text);
          // Manejar comandos especiales si es necesario
        } else {
          // Respuesta normal - mostrar en chat
          const aiMessage: ChatMessage = {
            id: `ai-${Date.now()}`,
            text: text,
            sender: "ai",
            timestamp: new Date(),
          };
          setMessages((prev) => [...prev, aiMessage]);

          // Reproducir audio si está disponible
          if (audioUrl) {
            console.log("🔊 Playing audio response...");
            await playAudio(audioUrl);
          }
        }
      } catch (error) {
        console.error("❌ Error processing reply:", error);
      }
    });

    socket.on("error", (error) => {
      console.error("❌ Socket error:", error);
    });

    socket.on("connect_error", (error) => {
      console.error("❌ Socket connect error:", error);
    });

    socket.on("disconnect", (reason) => {
      console.log("🔌 Desconectado:", reason);
      setIsSocketConnected(false);

      if (reason === "io server disconnect") {
        console.log("🔄 Intentando reconectar...");
        // El socket se reconectará automáticamente
      }
    });

    socket.on("reconnect", () => {
      console.log("🔄 Reconectado exitosamente");
      setIsSocketConnected(true);
      setVoiceError(null);
    });
  };

  const testConversation = async () => {
    console.log("🧪 Activando conversación por voz...");

    if (!session) {
      console.error("❌ No hay sesión activa para probar");
      return;
    }

    try {
      // Si la voz no está activa, activarla
      if (!isVoiceActive) {
        console.log("🎤 Activando micrófono para conversación...");
        await setupTranscription();
      }

      // Asegurar que el socket esté conectado
      if (!isSocketConnected) {
        console.log("🔌 Conectando socket para conversación...");
        await connectSocket();
      }

      console.log("✅ Conversación por voz lista - habla ahora");

      // Opcional: mostrar mensaje en el chat indicando que está listo
      const readyMessage: ChatMessage = {
        id: `ready-${Date.now()}`,
        text: "🎤 Conversación por voz activada. Habla ahora...",
        sender: "ai",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, readyMessage]);

    } catch (error) {
      console.error("❌ Error activando conversación por voz:", error);

      // Mostrar mensaje de error en el chat
      const errorMessage: ChatMessage = {
        id: `error-voice-${Date.now()}`,
        text: "Error al activar la conversación por voz. Verifica los permisos del micrófono.",
        sender: "ai",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    }
  };

  const testServerResponse = () => {
    console.log("🧪 Probando respuesta del servidor...");

    if (!socketRef.current?.connected) {
      console.error("❌ Socket no conectado");
      return;
    }

    // Probar diferentes eventos
    const testEvents = [
      { event: "test", data: { message: "hello" } },
      { event: "ping", data: {} },
      { event: "hello", data: { test: true } },
      { event: "transcribe", data: { text: "hola" } },
      { event: "message", data: { content: "test message" } }
    ];

    testEvents.forEach((test, index) => {
      setTimeout(() => {
        console.log(`📤 Enviando evento '${test.event}':`, test.data);
        socketRef.current?.emit(test.event, test.data);
      }, index * 1000); // Enviar cada evento con 1 segundo de diferencia
    });

    // Timeout para verificar respuestas
    setTimeout(() => {
      console.log("⏰ Test de eventos completado. Revisa si hubo respuestas del servidor.");
    }, 6000);
  };

  // ========== INICIALIZACIÓN AUTOMÁTICA ==========
  useEffect(() => {
    const initializeVoiceConversation = async () => {
      try {
        console.log("🚀 Inicializando conversación de voz...");

        // 1. Conectar socket
        await connectSocket();
        console.log("✅ Socket conectado");

        // 2. Configurar audio (esperar un poco para asegurar conexión)
        setTimeout(async () => {
          await setupTranscription();
          console.log("✅ Conversación de voz lista");
        }, 1000);
      } catch (error) {
        console.error("❌ Error inicial:", error);
        setVoiceError(
          error instanceof Error ? error.message : "Error de inicialización"
        );
      }
    };

    initializeVoiceConversation();

    // Cleanup al desmontar
    return () => {
      if (socketRef.current) {
        console.log("🧹 Disconnecting voice socket...");
        socketRef.current.disconnect();
        socketRef.current = null;
      }
      stopTranscription();
    };
  }, []);

  // Toggle de activación de voz
  const toggleVoice = async () => {
    if (isVoiceActive) {
      console.log("🔇 Deteniendo transcripción...");
      await stopTranscription();
    } else {
      console.log("🎤 Iniciando transcripción...");
      await setupTranscription();
    }
  };

  // Sync messages from game session
  useEffect(() => {
    if (session?.messages) {
      const chatMessages: ChatMessage[] = session.messages.map(
        (msg: SessionMessage): ChatMessage => ({
          id: msg.id,
          text: msg.text,
          sender: msg.sender,
          timestamp: msg.timestamp,
        })
      );
      setMessages(chatMessages);
    }
  }, [session?.messages]);

  useEffect(() => {
    if (
      session &&
      (!session.messages || session.messages.length === 0) &&
      messages.length === 0
    ) {
      const sendInitialHola = async () => {
        try {
          setIsLoading(true);
          await askInitialMessage("Hola");
        } catch (error) {
          console.error("Error sending initial Hola:", error);
        } finally {
          setIsLoading(false);
        }
      };

      sendInitialHola();
    }
  }, [session, messages.length, askInitialMessage]);

  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading || !session) return;

    const messageText = inputText.trim();
    setInputText("");
    setIsLoading(true);

    try {
      await askQuestion(messageText);
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}`,
        text: "Lo siento, hubo un error al procesar tu mensaje. Inténtalo de nuevo.",
        sender: "ai",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      <div className="chat-view">
        <div className="menu-left">
          <div className="enygma-logo">
            <button
              onClick={testConversation}
              style={{
                position: "relative",
                top: "-105px",
                right: "10px",
                background: isVoiceActive ? "green" : "blue",
                color: "white",
                padding: "10px",
                border: "none",
                borderRadius: "5px",
                cursor: "pointer",
                marginRight: "5px",
              }}
            >
              {isVoiceActive ? "🎤 Voz Activa" : "🎤 Activar Voz"}
            </button>

            <button
              onClick={testServerResponse}
              style={{
                position: "relative",
                top: "-105px",
                right: "10px",
                background: isSocketConnected ? "orange" : "gray",
                color: "white",
                padding: "10px",
                border: "none",
                borderRadius: "5px",
                cursor: "pointer",
              }}
            >
              🧪 Test Server
            </button>

            <Image
              src="assets/game/enygma.png"
              alt="Enygma"
              className="enygma-image"
              width="180px"
              aspectRatio="1:1"
            />

            <div className="speaking">
              <Mic
                level={Math.round(micLevel * 100)}
                onClick={toggleVoice}
                state={
                  isVoiceActive
                    ? "recording"
                    : voiceError
                      ? "disabled"
                      : "default"
                }
              />
              {voiceError && (
                <div
                  className="voice-error"
                  style={{ color: "red", fontSize: "12px", marginTop: "4px" }}
                >
                  {voiceError}
                </div>
              )}
              {isSocketConnected && (
                <div
                  style={{ color: "green", fontSize: "12px", marginTop: "4px" }}
                >
                  🟢 Conectado
                </div>
              )}
              {!isSocketConnected && (
                <div
                  style={{
                    color: "orange",
                    fontSize: "12px",
                    marginTop: "4px",
                  }}
                >
                  🟡 Desconectado
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="chat-view-wrapper">
          <div className="chat-container">
            <div
              className={`chat-content ${
                messages.length > 0 &&
                messages[messages.length - 1].sender === "user"
                  ? "align-right"
                  : "align-left"
              }`}
            >
              <div className="chat-text body1">
                {isAILoading
                  ? "🤔 Procesando..."
                  : messages.length > 0
                    ? messages[messages.length - 1].text
                    : isSocketConnected
                      ? "🎤 Habla para comenzar..."
                      : "⏳ Conectando..."}
              </div>
            </div>
          </div>

          <div className="chat-input-container">
            <div className="input-wrapper">
              <input
                type="text"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Haz una pregunta sobre el personaje..."
                disabled={isLoading || !session}
                className="chat-input"
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputText.trim() || isLoading || !session}
                className="send-button"
              >
                {isLoading ? "Enviando..." : "Enviar"}
              </button>
            </div>
          </div>
        </div>

        <div className="menu-right">
          <div
            onClick={() => setIsLivesPopUpShown((prev) => !prev)}
            className="image-button"
          >
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/lives.png"
              alt="Vidas"
              className="book-image"
            />

            {session && (
              <p
                className={`body2 bold questions-color-text ${questionsColorData.colorClass}`}
                style={{
                  color: questionsColorData.hexColor,
                }}
              >
                {session?.maxQuestions! - session?.questionCount!}
              </p>
            )}
          </div>

          <div onClick={handleShowClues} className="image-button">
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/clues.png"
              alt="Pistas"
              className="clues-image"
            />
            <p className="body2 bold">Pistas</p>
          </div>

          <div onClick={handleExistGame} className="image-button">
            <Image
              width="100%"
              aspectRatio="1:1"
              src="assets/game/exit.png"
              alt="Salir"
              className="exit-image"
            />
            <p className="body2 bold">Salir</p>
          </div>
        </div>
      </div>

      {showExitPopup && (
        <Modal
          title="¿Seguro que quieres salir del juego?"
          onClose={handleCancelExit}
          onCancel={handleConfirmExit}
          onConfirm={handleCancelExit}
          cancelText="Salir de todos modos"
          confirmText="Seguir jugando"
          body="Si sales ahora, vas a perder tu progreso actual. Puedes seguir jugando o salir cuando quieras."
        />
      )}

      {isLivesPopUpShown && (
        <Modal
          title="Tus preguntas restantes"
          onClose={() => setIsLivesPopUpShown((prev) => !prev)}
          onConfirm={() => setIsLivesPopUpShown((prev) => !prev)}
          confirmText="Entendido"
          body=" Tienes un máximo de 20 preguntas para adivinar la respuesta. Cada
          vez que haces una, se descuenta del contador. Piensa bien cada
          pregunta: ¡cada una cuenta!"
        />
      )}
    </>
  );
};

export default PlayView;
